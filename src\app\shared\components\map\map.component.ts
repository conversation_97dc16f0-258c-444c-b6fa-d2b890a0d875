import { Component, ElementRef, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import * as L from 'leaflet';
import 'leaflet.markercluster';
import { GeolocationService } from '../../../core/services/geolocation.service';
import { RadarService } from '../../../core/services/radar.service';
import { AlertService } from '../../../core/services/alert.service';
import { Radar } from '../../../core/models/radar.model';
import { Localizacao } from '../../../core/models/radar.model';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-map',
  templateUrl: './map.component.html',
  styleUrls: ['./map.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule]
})
export class MapComponent implements OnInit, <PERSON><PERSON><PERSON>roy {
  @ViewChild('mapContainer', { static: true }) mapContainer!: ElementRef;

  private map!: L.Map;
  private userMarker!: <PERSON><PERSON>;
  private radarMarkers: L.Marker[] = [];
  private subscriptions = new Subscription();

  // Propriedades públicas para o template
  public mapLoaded = false;
  public showRadars = true;
  public showLocationStatus = true;
  public currentSpeed = 0;
  public nearbyRadarCount = 0;

  // Configurações de múltiplos provedores de tiles para fallback
  private tileProviders = [
    {
      name: 'OpenStreetMap',
      url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
      attribution: '© OpenStreetMap contributors'
    },
    {
      name: 'CartoDB Positron',
      url: 'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png',
      attribution: '© OpenStreetMap contributors © CARTO'
    },
    {
      name: 'OpenStreetMap DE',
      url: 'https://{s}.tile.openstreetmap.de/{z}/{x}/{y}.png',
      attribution: '© OpenStreetMap contributors'
    }
  ];

  // Ícones personalizados melhorados
  private userIcon = L.divIcon({
    html: `<div class="user-marker">
                 <div class="user-marker-inner"></div>
                 <div class="user-marker-pulse"></div>
               </div>`,
    iconSize: [24, 24],
    iconAnchor: [12, 12],
    popupAnchor: [0, -12],
    className: 'user-location-icon'
  });

  private radarIcon = L.divIcon({
    html: `<div class="radar-marker">
                 <div class="radar-icon">⚠️</div>
               </div>`,
    iconSize: [28, 28],
    iconAnchor: [14, 14],
    popupAnchor: [0, -14],
    className: 'radar-icon'
  });

  @Input() height: string = '400px';
  @Input() showControls: boolean = true;

  constructor(
    private geolocationService: GeolocationService,
    private radarService: RadarService,
    private alertService: AlertService
  ) { }

  async ngOnInit() {
    try {
      await this.initializeMap();
      await this.loadRadars();
      this.subscribeToLocationUpdates();
    } catch (error) {
      console.error('Erro ao inicializar mapa:', error);
      this.alertService.showAlert('Erro', 'Não foi possível carregar o mapa. Tentando novamente...');
      // Tentar novamente após um delay
      setTimeout(() => this.initializeMap(), 2000);
    }
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
    if (this.map) {
      this.map.remove();
    }
  }

  private async initializeMap(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Limpar mapa existente se houver
        if (this.map) {
          this.map.remove();
        }

        // Configurar altura do container primeiro
        this.mapContainer.nativeElement.style.height = this.height;

        // Configuração inicial do mapa centrado no Brasil
        this.map = L.map(this.mapContainer.nativeElement, {
          center: [-15.7801, -47.9292], // Centro do Brasil (Brasília)
          zoom: 5, // Zoom menor para mostrar mais área
          zoomControl: this.showControls,
          attributionControl: true,
          preferCanvas: true // Melhor performance
        });

        // Tentar carregar tiles com fallback
        this.loadTilesWithFallback(0).then(() => {
          this.mapLoaded = true;
          console.log('Mapa carregado com sucesso');

          // Invalidar tamanho após carregamento
          setTimeout(() => {
            if (this.map) {
              this.map.invalidateSize();
            }
          }, 500);

          resolve();
        }).catch(reject);

      } catch (error) {
        console.error('Erro ao criar mapa:', error);
        reject(error);
      }
    });
  }

  private async loadTilesWithFallback(providerIndex: number): Promise<void> {
    return new Promise((resolve, reject) => {
      if (providerIndex >= this.tileProviders.length) {
        reject(new Error('Todos os provedores de tiles falharam'));
        return;
      }

      const provider = this.tileProviders[providerIndex];
      console.log(`Tentando carregar tiles de: ${provider.name}`);

      const tileLayer = L.tileLayer(provider.url, {
        attribution: provider.attribution,
        maxZoom: 19,
        errorTileUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==' // Tile transparente para erros
      });

      // Event listeners para detectar sucesso/falha
      let resolved = false;

      tileLayer.on('tileerror', () => {
        if (!resolved) {
          console.warn(`Falha ao carregar tiles de ${provider.name}, tentando próximo...`);
          tileLayer.remove();
          this.loadTilesWithFallback(providerIndex + 1).then(resolve).catch(reject);
          resolved = true;
        }
      });

      tileLayer.on('tileload', () => {
        if (!resolved) {
          console.log(`Tiles carregados com sucesso de ${provider.name}`);
          resolved = true;
          resolve();
        }
      });

      // Timeout manual
      setTimeout(() => {
        if (!resolved) {
          console.warn(`Timeout ao carregar tiles de ${provider.name}`);
          tileLayer.remove();
          this.loadTilesWithFallback(providerIndex + 1).then(resolve).catch(reject);
          resolved = true;
        }
      }, 8000);

      // Adicionar ao mapa
      tileLayer.addTo(this.map);
    });
  }

  private subscribeToLocationUpdates(): void {
    console.log('MapComponent.subscribeToLocationUpdates() iniciado');

    if (!this.geolocationService.estaMonitorando) {
      console.log('Monitoramento não está ativo, tentando iniciar...');
      this.geolocationService.startWatching().then(async (success) => {
        console.log('Resultado do startWatching:', success);
        if (success) {
          this.setupLocationSubscriptions();
        } else {
          console.error('Falha ao iniciar monitoramento de localização');
          await this.handleLocationError();
        }
      }).catch(error => {
        console.error('Erro ao iniciar monitoramento:', error);
        this.alertService.showAlert('Erro', 'Erro ao configurar localização');
      });
    } else {
      console.log('Monitoramento já está ativo, configurando subscriptions...');
      this.setupLocationSubscriptions();
    }
  }

  private setupLocationSubscriptions(): void {
    console.log('MapComponent.setupLocationSubscriptions() iniciado');

    const locationSub = this.geolocationService.getCurrentLocation().subscribe({
      next: (location: Localizacao) => {
        console.log('Localização recebida em getCurrentLocation:', location);
        this.updateUserLocation(location);
        this.loadRadarsNearLocation(location);
      },
      error: (error) => {
        console.error('Erro ao obter localização:', error);
        this.alertService.showAlert('Erro', 'Não foi possível obter sua localização');
      }
    });

    this.subscriptions.add(locationSub);

    if (this.geolocationService.estaMonitorando) {
      console.log('Configurando watchPosition subscription...');
      const watchSub = this.geolocationService.watchPosition().subscribe({
        next: (location: Localizacao) => {
          console.log('Localização recebida em watchPosition:', location);
          this.updateUserLocation(location);
          this.checkNearbyRadars(location);
        },
        error: (error) => {
          console.error('Erro ao monitorar localização:', error);
        }
      });

      this.subscriptions.add(watchSub);
    } else {
      console.warn('Monitoramento não está ativo, não configurando watchPosition');
    }
  }

  private updateUserLocation(location: Localizacao): void {
    if (!this.mapLoaded || !this.map) return;

    const latlng = L.latLng(location.latitude, location.longitude);

    // Atualizar velocidade atual
    this.currentSpeed = Math.round(location.velocidade);

    if (this.userMarker) {
      this.userMarker.setLatLng(latlng);
    } else {
      this.userMarker = L.marker(latlng, { icon: this.userIcon })
        .addTo(this.map)
        .bindPopup(`
                    <div class="user-popup">
                        <strong>📍 Sua Localização</strong><br>
                        <span class="speed">Velocidade: ${location.velocidade.toFixed(1)} km/h</span><br>
                        <span class="accuracy">Precisão: ${location.precisao.toFixed(0)}m</span><br>
                        <small>Atualizado: ${new Date().toLocaleTimeString()}</small>
                    </div>
                `);
    }

    // Centralizar mapa na primeira localização
    if (!this.map.hasLayer(this.userMarker)) {
      this.map.setView(latlng, 15);
    }

    // Atualizar contagem de radares próximos
    this.updateNearbyRadarCount(location);
  }

  private updateNearbyRadarCount(location: Localizacao): void {
    this.radarService.getNearbyRadars(location.latitude, location.longitude, 5000).subscribe({
      next: (radares) => {
        this.nearbyRadarCount = radares.length;
      },
      error: (error) => {
        console.error('Erro ao contar radares próximos:', error);
        this.nearbyRadarCount = 0;
      }
    });
  }

  private async loadRadars(): Promise<void> {
    try {
      console.log('Carregando todos os radares...');

      // Carregar todos os radares primeiro
      this.radarService.getAllRadars().subscribe({
        next: (radares) => {
          console.log(`Carregados ${radares.length} radares`);
          if (radares && radares.length > 0) {
            this.clearRadarMarkers();
            this.addRadarMarkersWithClustering(radares);
          } else {
            console.warn('Nenhum radar encontrado');
          }
        },
        error: (error) => {
          console.error('Erro ao carregar radares:', error);
          this.alertService.showAlert('Aviso', 'Não foi possível carregar os radares. Tentando novamente...');
          // Tentar carregar radares locais como fallback
          this.loadLocalRadars();
        }
      });
    } catch (error) {
      console.error('Erro ao carregar radares:', error);
      this.loadLocalRadars();
    }
  }

  private loadLocalRadars(): void {
    // Dados de radares locais como fallback
    const radaresLocal: Radar[] = [
      {
        id: 'radar-sp-001',
        latitude: -23.550520,
        longitude: -46.633308,
        tipo: 'fixo',
        velocidadeMaxima: 60,
        direcao: 'norte',
        ativo: true,
        endereco: 'Av. Paulista, São Paulo - SP',
        dataAtualizacao: new Date()
      },
      {
        id: 'radar-rj-001',
        latitude: -22.906847,
        longitude: -43.172896,
        tipo: 'fixo',
        velocidadeMaxima: 80,
        direcao: 'leste',
        ativo: true,
        endereco: 'Copacabana, Rio de Janeiro - RJ',
        dataAtualizacao: new Date()
      },
      {
        id: 'radar-mg-001',
        latitude: -19.924501,
        longitude: -43.935223,
        tipo: 'semaforo',
        velocidadeMaxima: 40,
        ativo: true,
        endereco: 'Centro, Belo Horizonte - MG',
        dataAtualizacao: new Date()
      }
    ];

    console.log('Carregando radares locais como fallback');
    this.addRadarMarkersWithClustering(radaresLocal);
  }

  private loadRadarsNearLocation(location: Localizacao): void {
    this.radarService.getNearbyRadars(location.latitude, location.longitude, 50000).subscribe({
      next: (radares) => {
        console.log(`Encontrados ${radares.length} radares próximos`);
        if (radares && radares.length > 0) {
          this.clearRadarMarkers();
          this.addRadarMarkersWithClustering(radares);
        }
      },
      error: (error) => {
        console.error('Erro ao carregar radares próximos:', error);
      }
    });
  }

  private addRadarMarkersWithClustering(radares: Radar[]): void {
    if (!this.mapLoaded || !this.map) return;

    const markerClusterGroup = L.markerClusterGroup({
      chunkedLoading: true,
      chunkInterval: 200,
      chunkDelay: 50
    });

    radares.forEach(radar => {
      if (radar.ativo) {
        const marker = L.marker([radar.latitude, radar.longitude], { icon: this.radarIcon })
          .bindPopup(`
                        <div class="radar-popup">
                            <strong>🚨 Radar ${radar.tipo.toUpperCase()}</strong><br>
                            <span class="speed-limit">Limite: ${radar.velocidadeMaxima} km/h</span><br>
                            <span class="direction">Direção: ${radar.direcao ? radar.direcao.toUpperCase() : 'TODAS'}</span><br>
                            <span class="address">${radar.endereco || 'Endereço não informado'}</span><br>
                            <small>Atualizado: ${new Date(radar.dataAtualizacao).toLocaleDateString()}</small>
                        </div>
                    `);

        if (this.showRadars) {
          markerClusterGroup.addLayer(marker);
        }
        this.radarMarkers.push(marker);
      }
    });

    if (this.showRadars) {
      this.map.addLayer(markerClusterGroup);
    }

    console.log(`Adicionados ${this.radarMarkers.length} marcadores de radar`);
  }

  private clearRadarMarkers(): void {
    if (!this.map) return;

    this.radarMarkers.forEach(marker => {
      this.map.removeLayer(marker);
    });
    this.radarMarkers = [];
  }

  private checkNearbyRadars(userLocation: Localizacao): void {
    this.radarService.getNearbyRadars(
      userLocation.latitude,
      userLocation.longitude,
      1000
    ).subscribe(nearbyRadars => {
      nearbyRadars.forEach(radar => {
        const distance = this.calculateDistance(
          userLocation.latitude,
          userLocation.longitude,
          radar.latitude,
          radar.longitude
        );

        if (distance <= 500) {
          this.alertService.createAlert(
            'radar',
            `Radar a ${distance.toFixed(0)}m - Limite: ${radar.velocidadeMaxima}km/h`,
            radar,
            distance
          );
        }
      });
    });
  }

  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371e3;
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) *
      Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  }

  // Métodos públicos para controles
  public centerOnUser(): void {
    if (this.userMarker && this.map) {
      this.map.setView(this.userMarker.getLatLng(), 15);
    } else {
      this.alertService.showAlert('Aviso', 'Localização não disponível');
    }
  }

  public toggleRadars(): void {
    this.showRadars = !this.showRadars;

    if (this.showRadars) {
      // Recarregar radares
      this.loadRadars();
    } else {
      // Ocultar radares
      this.clearRadarMarkers();
    }

    this.alertService.showAlert('Info',
      this.showRadars ? 'Radares exibidos' : 'Radares ocultos');
  }

  public async reloadMap(): Promise<void> {
    try {
      this.mapLoaded = false;
      await this.initializeMap();
      await this.loadRadars();
      this.alertService.showAlert('Sucesso', 'Mapa recarregado com sucesso!');
    } catch (error) {
      console.error('Erro ao recarregar mapa:', error);
      this.alertService.showAlert('Erro', 'Não foi possível recarregar o mapa');
    }
  }

  private async handleLocationError(): Promise<void> {
    console.error('Erro de localização detectado');
    this.alertService.showAlert(
      'Localização Indisponível',
      'Não é possível obter sua localização. O mapa será centrado no Brasil.'
    );

    // Centralizar no Brasil como fallback
    if (this.map) {
      this.map.setView([-15.7801, -47.9292], 5);
    }
  }
}
