.map-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.map-leaflet {
    width: 100%;
    height: 100%;
    min-height: 300px;
    z-index: 1;
}

.map-controls {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1000;

    ion-fab {
        pointer-events: auto;
    }

    ion-fab-button {
        --background: var(--ion-color-primary);
        --color: white;
        --box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);

        &:hover {
            --background: var(--ion-color-primary-shade);
        }
    }
}

// Estilos específicos para os popups do Leaflet
:host ::ng-deep {
    .leaflet-popup-content-wrapper {
        border-radius: 12px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        background: white;
    }

    .leaflet-popup-content {
        margin: 16px 20px;
        line-height: 1.5;
        font-size: 14px;

        .user-popup {
            text-align: center;

            strong {
                color: var(--ion-color-primary);
                font-weight: 600;
                font-size: 16px;
            }

            .speed {
                color: var(--radar-speed-color, #00e676);
                font-weight: 500;
            }

            .accuracy {
                color: var(--ion-color-medium);
            }

            small {
                color: var(--ion-color-medium);
                font-size: 12px;
            }
        }

        .radar-popup {
            text-align: center;
            min-width: 200px;

            strong {
                color: var(--radar-danger-color, #f44336);
                font-weight: 600;
                font-size: 16px;
                display: block;
                margin-bottom: 8px;
            }

            .speed-limit {
                color: var(--radar-danger-color, #f44336);
                font-weight: 600;
                font-size: 15px;
            }

            .direction {
                color: var(--ion-color-dark);
                font-weight: 500;
            }

            .address {
                color: var(--ion-color-medium);
                font-size: 13px;
                font-style: italic;
            }

            small {
                color: var(--ion-color-medium);
                font-size: 11px;
            }
        }
    }

    .leaflet-popup-tip {
        background: white;
        box-shadow: 0 3px 14px rgba(0, 0, 0, 0.15);
    }

    // Customizar controles de zoom
    .leaflet-control-zoom {
        a {
            background-color: var(--ion-color-light);
            color: var(--ion-color-dark);
            border: 1px solid var(--ion-color-medium);
            border-radius: 4px;

            &:hover {
                background-color: var(--ion-color-primary);
                color: white;
            }
        }
    }

    // Estilos para marcadores customizados
    .user-location-icon {
        .user-marker {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;

            .user-marker-inner {
                width: 16px;
                height: 16px;
                background-color: #2196F3;
                border: 3px solid white;
                border-radius: 50%;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
                z-index: 2;
            }

            .user-marker-pulse {
                position: absolute;
                width: 24px;
                height: 24px;
                background-color: rgba(33, 150, 243, 0.3);
                border-radius: 50%;
                animation: pulse 2s infinite;
                z-index: 1;
            }
        }
    }

    .radar-icon {
        .radar-marker {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;

            .radar-icon {
                width: 32px;
                height: 32px;
                background: linear-gradient(135deg, #FF5722, #F44336);
                border: 3px solid white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                box-shadow: 0 4px 12px rgba(255, 87, 34, 0.4);
                animation: radarPulse 3s infinite;
            }
        }
    }

    // Animações
    @keyframes pulse {
        0% {
            transform: scale(1);
            opacity: 1;
        }

        50% {
            transform: scale(1.2);
            opacity: 0.7;
        }

        100% {
            transform: scale(1.4);
            opacity: 0;
        }
    }

    @keyframes radarPulse {

        0%,
        100% {
            transform: scale(1);
            box-shadow: 0 4px 12px rgba(255, 87, 34, 0.4);
        }

        50% {
            transform: scale(1.05);
            box-shadow: 0 6px 16px rgba(255, 87, 34, 0.6);
        }
    }

    // Estilos para modo escuro
    @media (prefers-color-scheme: dark) {
        .leaflet-popup-content-wrapper {
            background-color: var(--ion-color-dark);
            color: var(--ion-color-light);
        }

        .leaflet-popup-tip {
            background: var(--ion-color-dark);
        }

        .leaflet-control-zoom a {
            background-color: var(--ion-color-dark);
            color: var(--ion-color-light);
            border: 1px solid var(--ion-color-medium);
        }

        .user-marker-inner {
            border-color: rgba(255, 255, 255, 0.8);
        }

        .radar-marker .radar-icon {
            border-color: rgba(255, 255, 255, 0.8);
        }
    }

    // Estilos para clusters de marcadores
    .marker-cluster-small {
        background-color: rgba(33, 150, 243, 0.6);
        color: white;
        border-radius: 50%;
        text-align: center;
        font-weight: bold;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .marker-cluster-medium {
        background-color: rgba(255, 152, 0, 0.6);
        color: white;
        border-radius: 50%;
        text-align: center;
        font-weight: bold;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .marker-cluster-large {
        background-color: rgba(244, 67, 54, 0.6);
        color: white;
        border-radius: 50%;
        text-align: center;
        font-weight: bold;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .marker-cluster {
        border: 3px solid white;

        div {
            width: 30px;
            height: 30px;
            margin-left: 5px;
            margin-top: 5px;
            text-align: center;
            border-radius: 15px;
            font: 12px "Helvetica Neue", Arial, Helvetica, sans-serif;
        }

        span {
            line-height: 30px;
        }
    }
}

// Responsividade
@media (max-width: 768px) {
    .map-container {
        border-radius: 0;
    }

    .map-controls {
        ion-fab-button {
            --size: 40px;
        }
    }

    :host ::ng-deep {
        .leaflet-popup-content {
            margin: 12px 16px;
            font-size: 13px;

            .radar-popup,
            .user-popup {
                min-width: 180px;
            }
        }

        .user-location-icon .user-marker {
            .user-marker-inner {
                width: 14px;
                height: 14px;
            }

            .user-marker-pulse {
                width: 20px;
                height: 20px;
            }
        }

        .radar-icon .radar-marker .radar-icon {
            width: 28px;
            height: 28px;
            font-size: 14px;
        }
    }
}

// Status de localização
.location-status {
    position: absolute;
    top: 16px;
    left: 16px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 12px 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 8px;

    .status-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 500;

        ion-icon {
            font-size: 16px;
        }
    }

    @media (prefers-color-scheme: dark) {
        background: rgba(0, 0, 0, 0.85);
        color: white;
    }
}

// Estados de carregamento
.map-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
    background-color: var(--ion-color-light);
    color: var(--ion-color-medium);
    flex-direction: column;

    ion-spinner {
        margin-bottom: 16px;
    }

    .loading-text {
        font-size: 14px;
        text-align: center;
    }
}
