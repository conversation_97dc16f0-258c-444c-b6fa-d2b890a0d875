<div class="map-container">
    <!-- Indicador de carregamento -->
    <div class="map-loading" *ngIf="!mapLoaded">
        <ion-spinner name="crescent" color="primary"></ion-spinner>
        <div class="loading-text">
            <strong>Carregando mapa...</strong><br>
            <small>Aguarde enquanto carregamos os dados</small>
        </div>
    </div>

    <!-- Container do mapa -->
    <div #mapContainer class="map-leaflet" [style.opacity]="mapLoaded ? '1' : '0'"></div>

    <!-- Controles flutuantes -->
    <div class="map-controls" *ngIf="showControls && mapLoaded">
        <!-- Botão centralizar na localização do usuário -->
        <ion-fab vertical="bottom" horizontal="end" slot="fixed">
            <ion-fab-button size="small" (click)="centerOnUser()" color="primary">
                <ion-icon name="locate"></ion-icon>
            </ion-fab-button>
        </ion-fab>

        <!-- Botão toggle radares -->
        <ion-fab vertical="bottom" horizontal="start" slot="fixed">
            <ion-fab-button size="small" (click)="toggleRadars()" [color]="showRadars ? 'danger' : 'medium'">
                <ion-icon [name]="showRadars ? 'radio' : 'radio-outline'"></ion-icon>
            </ion-fab-button>
        </ion-fab>

        <!-- Botão recarregar -->
        <ion-fab vertical="top" horizontal="end" slot="fixed">
            <ion-fab-button size="small" (click)="reloadMap()" color="secondary">
                <ion-icon name="refresh"></ion-icon>
            </ion-fab-button>
        </ion-fab>
    </div>

    <!-- Status de localização -->
    <div class="location-status" *ngIf="mapLoaded && showLocationStatus">
        <div class="status-item">
            <ion-icon name="speedometer" color="success"></ion-icon>
            <span>{{ currentSpeed }}km/h</span>
        </div>
        <div class="status-item">
            <ion-icon name="location" color="primary"></ion-icon>
            <span>{{ nearbyRadarCount }} radares próximos</span>
        </div>
    </div>
</div>
